import {
  App<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>po<PERSON>,
  Box,
  TextField,
  InputAdornment,
  IconButton,
  Badge,
  Tabs,
  Tab,
} from "@mui/material";
import {
  Search as SearchIcon,
  Notifications as NotificationsIcon,
  DarkMode,
  LightMode,
} from "@mui/icons-material";
import { useState } from "react";

function Header() {
  const [darkMode, setDarkMode] = useState(false);

  return (
    <AppBar position="static" color="default" elevation={1}>
      <Toolbar
        sx={{
          justifyContent: "space-between",
          minHeight: "64px",
          bgcolor: "#fff",
          borderBottom: 1,
          borderColor: "divider",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Tab label="Text" />
          <Tab label="Text" />
          <Tab label="Text" />
        </Box>

        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <TextField
            size="small"
            placeholder="Search"
            variant="outlined"
            sx={{ width: 200 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              ),
            }}
          />

          <Typography variant="body2" color="text.secondary">
            Text Text Text Text
          </Typography>

          <IconButton
            sx={{ p: 0, color: "#000" }}
            color="inherit"
            onClick={() => setDarkMode(!darkMode)}
          >
            {darkMode ? <DarkMode /> : <LightMode />}
          </IconButton>
        </Box>
      </Toolbar>
    </AppBar>
  );
}

export default Header;
